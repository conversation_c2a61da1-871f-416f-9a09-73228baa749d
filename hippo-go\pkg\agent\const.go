package agent

const (
	// 常用HTTP头常量
	HeaderAccept          = "application/json, text/javascript, */*; q=0.01"
	HeaderAcceptLanguage  = "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"
	HeaderContentTypeForm = "application/x-www-form-urlencoded; charset=UTF-8"
	HeaderOrigin          = "https://kyfw.12306.cn"
	HeaderUserAgent       = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
	HeaderXRequestedWith  = "XMLHttpRequest"

	// 安全相关头
	HeaderSecCHUA         = `"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`
	HeaderSecCHUAMobile   = "?0"
	HeaderSecCHUAPlatform = `"Windows"`
	HeaderSecFetchDest    = "empty"
	HeaderSecFetchMode    = "cors"
	HeaderSecFetchSite    = "same-origin"

	// 常用Referer
	RefererCommutation = "https://kyfw.12306.cn/otn/view/commutation_queryLeftTickets.html"
	RefererLogin       = "https://kyfw.12306.cn/otn/resources/login.html"

	// 主机名
	Host12306 = "kyfw.12306.cn"
)

const (
	BaseUrl = "https://www.12306.cn"

	conf    = "/index/otn/login/conf"
	ConfUrl = BaseUrl + conf

	BaseUrl2 = "https://kyfw.12306.cn"

	uamtkStatic    = "/passport/web/auth/uamtk-static"
	UamtkStaticUrl = BaseUrl2 + uamtkStatic

	createQR    = "/passport/web/create-qr64"
	CreateQrUrl = BaseUrl2 + createQR

	checkQR    = "/passport/web/checkqr"
	CheckQrUrl = BaseUrl2 + checkQR

	uamtk    = "/passport/web/auth/uamtk"
	UamtkUrl = BaseUrl2 + uamtk

	uamauthclient    = "/otn/uamauthclient"
	UamauthClientUrl = BaseUrl2 + uamauthclient

	conf2    = "/otn/login/conf"
	Conf2Url = BaseUrl2 + conf2

	initMy12306Api    = "/otn/index/initMy12306Api"
	InitMy12306ApiUrl = BaseUrl2 + initMy12306Api

	queryFavorOrderMergeInfoNew    = "/otn/npQuery/queryFavorOrderMergeInfoNew"
	QueryFavorOrderMergeInfoNewUrl = BaseUrl2 + queryFavorOrderMergeInfoNew

	transInfo    = "/otn/afterNateNP/transInfo"
	TransInfoUrl = BaseUrl2 + transInfo

	getTransInfo    = "/otn/afterNateNP/gettransInfo"
	GetTransInfoUrl = BaseUrl2 + getTransInfo

	queryFavorTrainDefine    = "/otn/npQuery/queryFavorTrainDefine"
	QueryFavorTrainDefineUrl = BaseUrl2 + queryFavorTrainDefine

	queryLeftTickets    = "/otn/leftTicket/queryU"
	QueryLeftTicketsUrl = BaseUrl2 + queryLeftTickets

	stationNamesJs    = "/otn/personalJS/core/common/station_name.js"
	StationNamesJsUrl = BaseUrl2 + stationNamesJs

	checkOrderInfo    = "/otn/npQuery/checkOrderInfo"
	CheckOrderInfoUrl = BaseUrl2 + checkOrderInfo

	confirmOrder    = "/otn/npQuery/confirmOrder"
	ConfirmOrderUrl = BaseUrl2 + confirmOrder
)
