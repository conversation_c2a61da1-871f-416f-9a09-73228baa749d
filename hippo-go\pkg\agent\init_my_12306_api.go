package agent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// My12306Response 我的12306响应结构
type My12306Response struct {
	ValidateMessagesShowId string                 `json:"validateMessagesShowId"`
	Status                 bool                   `json:"status"`
	HttpStatus             int                    `json:"httpstatus"`
	Data                   My12306Data            `json:"data"`
	Messages               []interface{}          `json:"messages"`
	ValidateMessages       map[string]interface{} `json:"validateMessages"`
}

// My12306Data 用户数据详情
type My12306Data struct {
	NotifyWay            string      `json:"notify_way"`
	IfShowAliQRCode      bool        `json:"if_show_ali_qr_code"`
	IsSuperUser          string      `json:"isSuperUser"`
	Email                string      `json:"_email"`
	UserStatus           string      `json:"user_status"`
	IsNeedModifyPassword interface{} `json:"_is_needModifyPassword"` // 可能为null
	NeedEdit             bool        `json:"needEdit"`
	MemberStatus         string      `json:"member_status"`
	ControlCode          interface{} `json:"control_code"` // 可能为null
	IdTypeCode           string      `json:"id_type_code"`
	UserName             string      `json:"user_name"`
	MemberLevel          string      `json:"member_level"`
	IsCanRegistMember    bool        `json:"isCanRegistMember"`
	UserRegard           string      `json:"user_regard"`
	ResetMemberPwd       string      `json:"resetMemberPwd"`
	IsActive             string      `json:"_is_active"`
}

func PostInitMy12306(targetURL string) (*My12306Response, error) {
	// 创建空body请求
	req, err := http.NewRequest("POST", targetURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {"0"},
		"Cookie":             {""}, // 由httpClient自动管理
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/index.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debug("请求体: [空]")

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var my12306Resp My12306Response
	if err := json.Unmarshal(body, &my12306Resp); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v\n原始响应: %s", err, body)
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &my12306Resp, nil
}
