package agent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// LoginConfResponse 登录配置响应结构
type LoginConfResponse struct {
	ValidateMessagesShowId string                 `json:"validateMessagesShowId"`
	Status                 bool                   `json:"status"`
	HttpStatus             int                    `json:"httpstatus"`
	Data                   LoginConfData          `json:"data"`
	Messages               []interface{}          `json:"messages"`
	ValidateMessages       map[string]interface{} `json:"validateMessages"`
}

// LoginConfData 配置数据详情
type LoginConfData struct {
	IsOpenUpdateHBTime string   `json:"is_open_updateHBTime"`
	IsStudentDate      bool     `json:"isstudentDate"`
	IsMessagePassCode  string   `json:"is_message_passCode"`
	BornDate           string   `json:"born_date"`
	IsPhoneCheck       string   `json:"is_phone_check"`
	EiEmail            string   `json:"ei_email"`
	StudentDate        []string `json:"studentDate"`
	IsUAMLogin         string   `json:"is_uam_login"`
	IsLoginPassCode    string   `json:"is_login_passCode"`
	UserName           string   `json:"user_name"`
	IsSweepLogin       string   `json:"is_sweep_login"`
	QueryUrl           string   `json:"queryUrl"`
	NowStr             string   `json:"nowStr"`
	PsrQrCodeResult    string   `json:"psr_qr_code_result"`
	Now                int64    `json:"now"`
	YYOpenTime         string   `json:"yy_open_time"`
	Name               string   `json:"name"`
	LoginUrl           string   `json:"login_url"`
	StuControl         int      `json:"stu_control"`
	IsLogin            string   `json:"is_login"`
	IsOlympicLogin     string   `json:"is_olympicLogin"`
	YYTicketQuery      string   `json:"yy_ticket_query"`
	OtherControl       int      `json:"other_control"`
}

func PostLoginConf(targetURL string) (*LoginConfResponse, error) {
	// 创建空body请求
	req, err := http.NewRequest("POST", targetURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {"0"}, // 明确设置为0
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/index.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debug("请求体: [空]")

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var confResp LoginConfResponse
	if err := json.Unmarshal(body, &confResp); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v\n原始响应: %s", err, body)
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &confResp, nil
}
