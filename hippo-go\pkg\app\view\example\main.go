package main

import (
	"math/rand"
	"strconv"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"github.com/hypnoswang/hippo-go/pkg/app/view"
	"github.com/hypnoswang/hippo-go/pkg/global"
)

func main() {
	myApp := app.New()
	myWindow := myApp.NewWindow("计次票卡片视图测试")
	myWindow.Resize(fyne.NewSize(800, 600)) // 调整窗口大小适应卡片布局

	appView := &view.AppView{MainWindow: myWindow}
	refreshBtn := widget.NewButton("刷新数据", nil)

	// 封装内容更新逻辑
	updateContent := func() {
		data := generateTestData(20) // 减少测试数据量，卡片布局需要更多空间
		content := appView.CreateJicipiaoView(data, func(favor *global.FavorOrderData, batch *global.BatchItem) {
			// 模拟预订操作
			println("预订按钮点击:")
			println("  订单ID:", favor.OrderId)
			println("  批次号:", batch.BatchNo)
			println("  用户名:", batch.UserName)
			println("  出发站:", batch.FromStationName)
			println("  到达站:", batch.ToStationName)
			println("  剩余次数:", batch.ValidCount)
		})

		// 创建带刷新按钮的主布局
		mainContent := container.NewBorder(
			nil,        // top
			refreshBtn, // bottom
			nil,        // left
			nil,        // right
			content,    // center
		)

		myWindow.SetContent(mainContent)
	}

	refreshBtn.OnTapped = updateContent
	updateContent() // 初始加载

	myWindow.ShowAndRun()
}

// 生成测试数据
func generateTestData(count int) []*global.FavorOrderData {
	var jicipiaos []*global.FavorOrderData
	stations := []string{
		"北京南-上海虹桥",
		"广州南-深圳北",
		"成都东-重庆西",
		"武汉-长沙南",
		"南京南-杭州东",
	}
	names := []string{"张三", "李四", "王五", "赵六", "钱七"}

	for i := 0; i < count; i++ {
		batchCount := 1 + i%3 // 每个Jicipiao有1-3个Batch
		var batches []*global.BatchItem

		for j := 0; j < batchCount; j++ {
			batch := &global.BatchItem{
				BatchNo:             "B" + strconv.Itoa(i*10+j),
				UserName:            names[(i+j)%len(names)],
				InvalidDate:         time.Now().AddDate(0, i%12, j*5).Format("2006-01-02"),
				UseCount:            strconv.Itoa(10 + i%10),
				ValidDay:            strconv.Itoa(30 + i%60),
				FromStationName:     "起始站" + strconv.Itoa(i%10),
				ToStationName:       "终到站" + strconv.Itoa(i%10),
				UserEncStr:          "ENC" + strconv.Itoa(i),
				ValidCount:          strconv.Itoa(rand.Intn(15)), // 限制最大剩余次数，使部分卡片有预订按钮
				FavorId:             "F" + strconv.Itoa(i*100+j),
				FromStationTelecode: "WWP", // 示例电报码
				ToStationTelecode:   "VNP", // 示例电报码
			}

			batches = append(batches, batch)
		}

		jicipiaos = append(jicipiaos, &global.FavorOrderData{
			OrderId:         "ORDER" + strconv.Itoa(i+1000), // 添加订单ID
			FavorClass:      "P200E",                        // 添加票种类别
			FavorClassName:  "次票类型" + strconv.Itoa(i%5+1),
			StationsName:    stations[i%len(stations)],
			ReserverName:    names[i%len(names)],
			ActiveLimitTime: time.Now().AddDate(0, 0, i*3).Format("2006-01-02"),
			BatchList:       batches,
			ReserverEncStr:  "RES" + strconv.Itoa(i),
			Effective:       true, // 确保所有测试数据都有效
		})
	}

	return jicipiaos
}
