package controller

import (
	"encoding/json"
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"github.com/hypnoswang/hippo-go/pkg/agent"
	"github.com/hypnoswang/hippo-go/pkg/app/model"
	"github.com/hypnoswang/hippo-go/pkg/app/view"
	"github.com/hypnoswang/hippo-go/pkg/global"
	"github.com/hypnoswang/hippo-go/pkg/log"
)

type AppController struct {
	model *model.AppModel
	view  *view.AppView

	agent_ global.Agent12306
}

func NewAppController(model *model.AppModel, view *view.AppView) *AppController {
	return &AppController{
		model:  model,
		view:   view,
		agent_: agent.NewAgent(),
	}

}

//nc (c *AppController) SetupUI() {
/// 创建导航栏
//avStart := widget.NewButton("开始", c.onStart)
//
//avTasks := widget.NewButton("我的任务", func() {
//c.view.RightContent.Objects = nil
//c.view.RightContent.Add(c.view.TasksContent)
//)
//
//eftNav := container.NewVBox(
//navStart,
//navTasks,
//widget.NewSeparator(),
//
//
/// 创建主界面布局
//plit := container.NewHSplit(
//container.NewBorder(nil, nil, nil, nil, leftNav),
//container.NewBorder(nil, nil, nil, nil, c.view.RightContent),
//
//plit.SetOffset(0.2)
//
//.view.MainWindow.SetContent(split)
//

func (c *AppController) SetupUI() {
	// 设置Tab按钮点击事件
	c.view.StartTab.OnTapped = func() {
		c.view.SwitchTab("start")
		c.onStart()
	}
	c.view.TasksTab.OnTapped = func() {
		c.view.SwitchTab("tasks")
	}

	// 创建主界面布局
	content := container.NewBorder(
		nil,                      // top
		c.view.SetupBottomTabs(), // bottom (Tab栏)
		nil,                      // left
		nil,                      // right
		c.view.RightContent,      // center
	)

	c.view.MainWindow.SetContent(content)
	//view.SwitchTab("start") // 默认显示开始页
}

func (c *AppController) onStart() {
	if !c.agent_.CheckLogin2() { // 已登录，直接进入任务页面
		c.view.RightContent.Objects = nil
		qrLoginView := view.CreateQrLoginView(c)
		c.view.RightContent.Add(qrLoginView)
	} else {
		c.model.IsLogin = true
		c.view.RightContent.Objects = nil
		c.view.RightContent.Add(container.NewVBox(widget.NewLabel("请稍候")))
		go c.onSwept()
	}
}

func (c *AppController) HandleQrLogin() {
	var msg string

	qrCode, err := c.agent_.GetQrCode()
	if err != nil {
		msg = "获取二维码失败：" + err.Error()
		c.view.ShowPopup(msg, time.Second*2)
		return
	} else {
		qrView := c.view.CreateQrView(qrCode.Image)
		if qrView == nil {
			msg = "创建二维码视图失败。"
			c.view.ShowPopup(msg, time.Second*2)
			return
		} else {
			c.view.FlushContent(qrView)
		}

		go func() {
			waitSeconds := 300
			for i := range waitSeconds {
				swept, err := c.agent_.CheckQrCode(qrCode.Uuid)
				if err != nil {
					fyne.CurrentApp().Driver().DoFromGoroutine(func() {
						c.view.ShowPopup(err.Error(), 2*time.Second)
					}, false)
					return
				} else {
					if !swept {
						if i == waitSeconds-1 {
							fyne.CurrentApp().Driver().DoFromGoroutine(func() {
								c.view.ShowPopup("等待扫码超时", 2*time.Second)
							}, false)
							return
						}
						time.Sleep(time.Second)
					} else {
						fyne.CurrentApp().Driver().DoFromGoroutine(func() {
							c.view.ShowPopup("扫码成功！", 2*time.Second)
							c.onSwept()
						}, false)
						break
					}

				}
			}
		}()
	}
}

// 注意： 这个方法不是在主协程执行的
func (c *AppController) onSwept() {
	var msg string

	err := c.agent_.InitApi()
	if err != nil {
		fyne.CurrentApp().Driver().DoFromGoroutine(func() {
			msg = "初始化API失败：" + err.Error()
			c.view.ShowPopup(msg, 2*time.Second)
		}, false)
		return
	}

	login := c.agent_.CheckLogin2()
	if !login {
		fyne.CurrentApp().Driver().DoFromGoroutine(func() {
			msg = "登录失败，请重试。"
			c.view.ShowPopup(msg, 2*time.Second)
		}, false)
		return
	} else {
		fyne.CurrentApp().Driver().DoFromGoroutine(func() {
			msg = "登录成功，请稍后。"
			c.view.ShowPopup(msg, 3*time.Second)

			jicipiaos, err := c.agent_.QueryJicipiaos()
			if err != nil {
				msg := "查询失败：" + err.Error()
				c.view.ShowPopup(msg, 3*time.Second)
				return
			}

			jView := c.view.CreateJicipiaoView(jicipiaos, c.onBookJicipiao)
			if jView == nil {
				msg := "创建定期票列表失败"
				c.view.ShowPopup(msg, 3*time.Second)
				return
			}

			c.view.FlushContent(jView)

		}, false)

		return
	}
}

func (c *AppController) onBookJicipiao(favor *global.FavorOrderData, batch *global.BatchItem) {
	// 显示日期选择对话框
	c.showDateSelectionDialog(favor, batch)
}

// showDateSelectionDialog 显示日期选择对话框
func (c *AppController) showDateSelectionDialog(favor *global.FavorOrderData, batch *global.BatchItem) {
	// 生成可选日期列表（今天开始的14天）
	today := time.Now()
	var dateOptions []string
	var dateValues []time.Time

	for i := 0; i < 14; i++ {
		date := today.AddDate(0, 0, i)
		dateOptions = append(dateOptions, date.Format("2006-01-02 (Monday)"))
		dateValues = append(dateValues, date)
	}

	// 创建日期选择下拉框
	dateSelect := widget.NewSelect(dateOptions, nil)
	dateSelect.SetSelected(dateOptions[0]) // 默认选择今天

	// 创建路线信息标签
	routeInfo := widget.NewLabel(fmt.Sprintf("路线: %s → %s", batch.FromStationName, batch.ToStationName))
	routeInfo.Wrapping = fyne.TextWrapWord

	// 创建票种信息标签
	ticketInfo := widget.NewLabel(fmt.Sprintf("票种: %s", favor.FavorClassName))

	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel("请选择乘车日期"),
		widget.NewSeparator(),
		routeInfo,
		ticketInfo,
		widget.NewSeparator(),
		widget.NewLabel("乘车日期:"),
		dateSelect,
	)

	// 创建按钮容器
	var dialog *widget.PopUp

	buttonContainer := container.NewHBox(
		widget.NewButton("取消", func() {
			if dialog != nil {
				dialog.Hide()
			}
		}),
		widget.NewButton("确认预订", func() {
			selectedIndex := -1
			for i, option := range dateOptions {
				if option == dateSelect.Selected {
					selectedIndex = i
					break
				}
			}

			if selectedIndex >= 0 {
				selectedDate := dateValues[selectedIndex]
				if dialog != nil {
					dialog.Hide()
				}
				c.processBooking(favor, batch, selectedDate)
			}
		}),
	)

	// 创建对话框
	dialog = widget.NewModalPopUp(
		container.NewBorder(
			content,
			buttonContainer,
			nil, nil, nil,
		),
		c.view.MainWindow.Canvas(),
	)

	// 设置对话框大小和显示
	dialog.Resize(fyne.NewSize(400, 300))
	dialog.Show()
}

// processBooking 处理预订逻辑
func (c *AppController) processBooking(favor *global.FavorOrderData, batch *global.BatchItem, selectedDate time.Time) {
	// 显示预订进度
	c.view.ShowPopup("正在查询车次信息...", 2*time.Second)

	go func() {
		passengerInfo := fmt.Sprintf("%s/%s/%s/%s/1/c/1",
			batch.UserName, batch.UserIdNo, batch.UserIdType, batch.UserEncStr)
		batchInfo, _ := json.Marshal(batch)
		// 2. 调用 otn/afterNateNP/transInfo 提交转运信息
		transInfoReq := agent.TransInfoRequest{
			PassengerInfos: passengerInfo,
			OrderID:        favor.OrderId,
			BatchNo:        string(batchInfo),
		}

		err := agent.PostTransInfo(agent.TransInfoUrl, transInfoReq)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("提交转运信息失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		// 1. 调用 otn/afterNateNP/gettransInfo 获取转运信息
		_, err = agent.PostGetTransInfo(agent.GetTransInfoUrl)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("获取转运信息失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		loginResp, _ := agent.PostLoginConf(agent.Conf2Url)
		log.Debugf("loginResp: %+v", loginResp)

		time.Sleep(time.Second)

		// 1. 调用 otn/npQuery/queryFavorTrainDefine 查询定期票车次定义
		trainDefineReq := agent.FavorTrainDefineRequest{
			FavorClass:          favor.FavorClass,
			FavorId:             batch.FavorId,
			TrainDate:           selectedDate.Format("20060102"), // yyyyMMdd格式
			FromStationTelecode: batch.FromStationTelecode,
			ToStationTelecode:   batch.ToStationTelecode,
		}

		trainDefineResp, err := agent.PostQueryFavorTrainDefine(agent.QueryFavorTrainDefineUrl, trainDefineReq)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("查询车次定义失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		if !trainDefineResp.Status || trainDefineResp.HttpStatus != 200 {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("查询车次定义失败: 服务器返回错误", 3*time.Second)
			}, false)
			return
		}

		// 2. 调用 otn/leftTicket/queryU 查询余票信息
		fyne.CurrentApp().Driver().DoFromGoroutine(func() {
			c.view.ShowPopup("正在查询余票信息...", 2*time.Second)
		}, false)

		leftTicketReq := agent.LeftTicketQueryParams{
			TrainDate:   selectedDate.Format("2006-01-02"), // yyyy-MM-dd格式
			FromStation: batch.FromStationTelecode,
			ToStation:   batch.ToStationTelecode,
			PurposeCode: "ADULT", // 默认成人票
		}

		leftTicketResp, err := agent.GetLeftTicketList(agent.QueryLeftTicketsUrl, leftTicketReq)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("查询余票失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		if !leftTicketResp.Status || leftTicketResp.HttpStatus != 200 {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("查询余票失败: 服务器返回错误", 3*time.Second)
			}, false)
			return
		}

		// 3. 处理查询结果，显示可用车次
		// usableTrains := trainDefineResp.Data.ParseUsableTrains()
		// availableTrains := leftTicketResp.Data.ParseTrainList()

		//fyne.CurrentApp().Driver().DoFromGoroutine(func() {
		//	c.showTrainSelectionDialog(favor, batch, selectedDate, usableTrains, availableTrains)
		//}, false)
	}()
}

// showTrainSelectionDialog 显示车次选择对话框
func (c *AppController) showTrainSelectionDialog(favor *global.FavorOrderData, batch *global.BatchItem, selectedDate time.Time, usableTrains []string, availableTrains []string) {
	// 过滤出既可用又有余票的车次
	var validTrains []string
	for _, usable := range usableTrains {
		for _, available := range availableTrains {
			if usable == available {
				validTrains = append(validTrains, usable)
				break
			}
		}
	}

	if len(validTrains) == 0 {
		c.view.ShowPopup("抱歉，当前日期没有可用的车次", 3*time.Second)
		return
	}

	// 创建车次信息显示
	routeInfo := widget.NewLabel(fmt.Sprintf("路线: %s → %s", batch.FromStationName, batch.ToStationName))
	dateInfo := widget.NewLabel(fmt.Sprintf("日期: %s", selectedDate.Format("2006-01-02")))

	// 创建车次列表
	trainList := widget.NewList(
		func() int { return len(validTrains) },
		func() fyne.CanvasObject {
			return widget.NewLabel("车次")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(validTrains[id])
		},
	)
	trainList.Resize(fyne.NewSize(300, 200))

	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel("查询结果"),
		widget.NewSeparator(),
		routeInfo,
		dateInfo,
		widget.NewLabel(fmt.Sprintf("找到 %d 个可用车次:", len(validTrains))),
		trainList,
	)

	// 创建按钮容器
	var dialog *widget.PopUp

	buttonContainer := container.NewHBox(
		widget.NewButton("关闭", func() {
			if dialog != nil {
				dialog.Hide()
			}
		}),
		widget.NewButton("继续预订", func() {
			if dialog != nil {
				dialog.Hide()
			}
			// 继续预订流程
			c.continueBookingProcess(favor, batch, selectedDate)
		}),
	)

	// 创建对话框
	dialog = widget.NewModalPopUp(
		container.NewBorder(
			content,
			buttonContainer,
			nil, nil, nil,
		),
		c.view.MainWindow.Canvas(),
	)

	// 设置对话框大小和显示
	dialog.Resize(fyne.NewSize(450, 400))
	dialog.Show()
}

// continueBookingProcess 继续预订流程
func (c *AppController) continueBookingProcess(favor *global.FavorOrderData, batch *global.BatchItem, selectedDate time.Time) {
	// 显示预订进度
	c.view.ShowPopup("正在处理预订...", 2*time.Second)

	go func() {
		// 1. 调用 otn/afterNateNP/gettransInfo 获取转运信息
		getTransResp, err := agent.PostGetTransInfo(agent.GetTransInfoUrl)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("获取转运信息失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		// 2. 调用 otn/afterNateNP/transInfo 提交转运信息
		transInfoReq := agent.TransInfoRequest{
			PassengerInfos: getTransResp.PassengerInfos,
			OrderID:        favor.OrderId,
			BatchNo:        batch.BatchNo,
		}

		err = agent.PostTransInfo(agent.TransInfoUrl, transInfoReq)
		if err != nil {
			fyne.CurrentApp().Driver().DoFromGoroutine(func() {
				c.view.ShowPopup("提交转运信息失败: "+err.Error(), 3*time.Second)
			}, false)
			return
		}

		// 预订成功
		fyne.CurrentApp().Driver().DoFromGoroutine(func() {
			successMsg := fmt.Sprintf("预订成功！\n日期: %s\n路线: %s → %s",
				selectedDate.Format("2006-01-02"),
				batch.FromStationName,
				batch.ToStationName)
			c.view.ShowPopup(successMsg, 5*time.Second)
		}, false)
	}()
}
