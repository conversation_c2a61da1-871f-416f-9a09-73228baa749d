# Hippo-Go 🦛

[![Go Version](https://img.shields.io/badge/Go-1.23.7-blue.svg)](https://golang.org/)
[![Fyne](https://img.shields.io/badge/GUI-Fyne%20v2.6.1-green.svg)](https://fyne.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Development Status](https://img.shields.io/badge/Status-In%20Development-orange.svg)]()

一个基于 Go 语言开发的 12306 智能抢票辅助工具，采用现代化的 GUI 界面设计，为用户提供便捷、高效的火车票预订体验。

## ✨ 主要功能

### 🔐 智能登录系统
- **二维码扫码登录**：支持 12306 官方二维码登录方式
- **自动登录状态检测**：智能检测用户登录状态，避免重复登录
- **会话持久化**：使用持久化 Cookie 技术，保持登录状态

### 🎫 票务管理功能
- **定期票查询**：支持查询用户的定期票（计次票）信息
- **余票实时查询**：实时查询指定线路的余票情况
- **车次信息解析**：智能解析车次、站点、时间等详细信息
- **批次管理**：支持定期票批次信息的查看和管理

### 🖥️ 现代化界面
- **跨平台 GUI**：基于 Fyne 框架，支持 Windows、macOS、Linux
- **响应式设计**：自适应界面布局，支持不同屏幕尺寸
- **实时状态反馈**：提供实时的操作状态提示和错误信息
- **卡片式布局**：采用现代化的卡片式设计，信息展示清晰

### 🔧 技术特性
- **模块化架构**：采用 MVC 设计模式，代码结构清晰
- **高性能日志**：集成 Zap 高性能日志系统，支持日志轮转
- **HTTP 优化**：支持 gzip/deflate 压缩，模拟真实浏览器行为
- **错误处理**：完善的错误处理机制，提供详细的错误信息

## 🏗️ 技术架构

### 核心技术栈
- **编程语言**：Go 1.23.7
- **GUI 框架**：Fyne v2.6.1
- **日志系统**：Zap + Lumberjack
- **HTTP 客户端**：原生 net/http + 持久化 Cookie

### 项目结构
```
hippo-go/
├── cmd/                    # 命令行工具入口
├── demo/                   # 演示程序
├── pkg/                    # 核心包
│   ├── agent/             # 12306 API 代理层
│   │   ├── agent.go       # 核心代理实现
│   │   ├── const.go       # 常量定义
│   │   ├── getqr.go       # 二维码获取
│   │   ├── checkqr.go     # 二维码验证
│   │   ├── query_*.go     # 各类查询接口
│   │   └── ...
│   ├── app/               # 应用层
│   │   ├── controller/    # 控制器（MVC-C）
│   │   ├── model/         # 数据模型（MVC-M）
│   │   └── view/          # 视图层（MVC-V）
│   ├── global/            # 全局定义
│   ├── log/               # 日志模块
│   └── utils/             # 工具函数
├── go.mod                 # Go 模块定义
└── README.md              # 项目说明
```

## 🔬 实现原理

### 1. 登录认证流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant App as Hippo-Go
    participant API as 12306 API

    User->>App: 启动应用
    App->>API: 检查登录状态
    alt 未登录
        App->>API: 获取二维码
        API-->>App: 返回二维码图片
        App->>User: 显示二维码
        User->>API: 扫码登录
        App->>API: 轮询检查扫码状态
        API-->>App: 登录成功
        App->>API: 初始化 API Token
    else 已登录
        App->>User: 直接进入主界面
    end
```

### 2. API 交互机制
- **Cookie 管理**：使用 `persistent-cookiejar` 实现 Cookie 的持久化存储
- **请求模拟**：完整模拟浏览器请求头，包括 User-Agent、Referer 等
- **响应处理**：自动处理 gzip/deflate 压缩响应
- **错误重试**：实现智能的错误重试机制

### 3. 数据流处理
```
用户操作 → Controller → Agent → 12306 API
    ↓           ↓         ↓         ↓
   View ← Model ← 数据解析 ← JSON 响应
```

### 4. 核心接口设计
```go
type Agent12306 interface {
    CheckLogin() bool                              // 检查登录状态
    GetQrCode() (*QrCode, error)                  // 获取登录二维码
    CheckQrCode(uuid string) (bool, error)        // 检查二维码扫描状态
    InitApi() error                               // 初始化 API
    QueryJicipiaos() ([]*FavorOrderData, error)   // 查询定期票信息
}
```

### 5. 安全特性
- **TLS 连接**：使用 HTTPS 确保数据传输安全
- **请求签名**：遵循 12306 官方 API 的安全要求
- **频率控制**：内置请求频率限制，避免被封禁
- **数据验证**：对所有输入数据进行严格验证

## 🚀 使用方式

> **注意**：本项目目前处于开发阶段，功能仍在完善中。

### 环境要求
- Go 1.23.7 或更高版本
- 支持的操作系统：Windows、macOS、Linux

### 快速开始
```bash
# 克隆项目
git clone https://github.com/hypnoswang/hippo-go.git
cd hippo-go

# 安装依赖
go mod tidy

# 运行演示程序
cd demo
go run main.go

# 或运行完整版本
cd pkg/app/demo
go run main.go
```

### 编译构建
```bash
# 构建可执行文件
go build -o hippo ./demo

# 交叉编译（Windows）
GOOS=windows GOARCH=amd64 go build -o hippo.exe ./demo

# 交叉编译（macOS）
GOOS=darwin GOARCH=amd64 go build -o hippo-mac ./demo
```

## 📋 开发计划

- [ ] 完善定期票预订功能
- [ ] 添加普通票抢票功能
- [ ] 实现多任务并发处理
- [ ] 添加配置文件支持
- [ ] 优化界面交互体验
- [ ] 增加单元测试覆盖
- [ ] 添加 CI/CD 流水线

## ⚠️ 免责声明

本软件仅供学习和研究使用，请遵守 12306 官方的使用条款和相关法律法规。使用本软件所产生的任何后果由用户自行承担。

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目！

## 📞 联系方式

- 项目地址：[https://github.com/hypnoswang/hippo-go](https://github.com/hypnoswang/hippo-go)
- 问题反馈：[Issues](https://github.com/hypnoswang/hippo-go/issues)

---

<div align="center">
  <sub>Built with ❤️ by <a href="https://github.com/hypnoswang">hypnoswang</a></sub>
</div>
