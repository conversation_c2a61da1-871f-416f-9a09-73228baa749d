package main

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"

	"github.com/hypnoswang/hippo-go/pkg/app/controller"
	"github.com/hypnoswang/hippo-go/pkg/app/model"
	"github.com/hypnoswang/hippo-go/pkg/app/view"
)

func main() {
	// 初始化应用
	myApp := app.New()
	myWindow := myApp.NewWindow("Hippo通勤")
	myWindow.Resize(fyne.NewSize(800, 600))

	// 初始化MVC组件
	appModel := model.NewAppModel(myWindow)
	appView := view.NewAppView(myWindow)
	appController := controller.NewAppController(appModel, appView)

	// 设置UI并运行
	appController.SetupUI()

	myWindow.ShowAndRun()
}
