package agent

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

func PostUAMAuthClient(targetURL, tk string) error {
	// 准备表单数据
	form := url.Values{}
	form.Set("tk", tk)
	formData := form.Encode()

	req, err := http.NewRequest("POST", targetURL, strings.NewReader(formData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {fmt.Sprintf("%d", len(formData))},
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/passport?redirect=/otn/login/userLogin"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debugf("请求体: %s\n", formData)

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	log.Debugf("\n====== 响应体 ======")
	log.Debugf("%s", body)

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return nil
}
