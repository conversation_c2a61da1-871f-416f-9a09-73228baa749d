package utils

import (
	"bytes"
	"compress/gzip"
	"compress/zlib"
	"fmt"
	"io"
	"net/http"

	"github.com/hypnoswang/hippo-go/pkg/log"
)

// DecompressResponse 根据响应头自动解压响应体
// 会调用resp.Body.Close()
func DecompressResponse(resp *http.Response) ([]byte, error) {
	defer resp.Body.Close()

	// 读取原始数据
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("读取响应失败: %v", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 根据Content-Encoding选择解压方式
	switch resp.Header.Get("Content-Encoding") {
	case "gzip":
		return decompressGzip(body)
	case "deflate":
		return decompressZlib(body)
	case "br":
		return nil, fmt.Errorf("brotli压缩暂不支持") // Go 1.16+可用compress/brotli
	default:
		return body, nil // 无压缩
	}
}

// 解压gzip数据
func decompressGzip(data []byte) ([]byte, error) {
	gr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		log.Errorf("gzip解压失败: %v", err)
		return nil, fmt.Errorf("gzip解压失败: %v", err)
	}
	defer gr.Close()

	return io.ReadAll(gr)
}

// 解压zlib数据
func decompressZlib(data []byte) ([]byte, error) {
	zr, err := zlib.NewReader(bytes.NewReader(data))
	if err != nil {
		log.Errorf("zlib解压失败: %v", err)
		return nil, fmt.Errorf("zlib解压失败: %v", err)
	}
	defer zr.Close()

	return io.ReadAll(zr)
}
