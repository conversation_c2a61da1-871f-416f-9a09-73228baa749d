package main

import (
	"fmt"
	"image/color"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
)

func main() {
	// 创建应用
	myApp := app.New()
	myWindow := myApp.NewWindow("Hippo通勤")
	myWindow.Resize(fyne.NewSize(800, 600))

	// 创建右侧内容区域
	rightContent := container.NewMax()

	// 模拟获取二维码图片的后台方法
	getQRCodeImage := func() fyne.CanvasObject {
		// 直接创建并返回一个红色矩形
		rect := canvas.NewRectangle(color.NRGBA{R: 255, G: 0, B: 0, A: 255})
		rect.SetMinSize(fyne.NewSize(200, 200))
		return rect
	}

	// 创建"开始"菜单的内容
	startContent := container.NewCenter( // 使用Center容器使内容居中
		container.NewVBox(
			container.NewHBox( // 使用HBox使按钮在水平方向居中
				layout.NewSpacer(),
				widget.NewButton("扫码登录", func() {
					// 清空右侧内容
					rightContent.Objects = nil

					// 获取并显示二维码（限制大小）
					qrCode := getQRCodeImage()
					qrContainer := container.NewCenter( // 二维码也居中显示
						container.NewStack(
							qrCode,
						),
					)
					rightContent.Add(qrContainer)
					rightContent.Refresh()
				}),
				layout.NewSpacer(),
			),
		),
	)

	// 创建"我的任务"菜单的内容
	tasksContent := container.NewVBox(
		widget.NewLabel("我的任务功能待实现"),
	)

	// 创建左侧导航栏
	navStart := widget.NewButton("开始", func() {
		rightContent.Objects = nil
		rightContent.Add(startContent)
		rightContent.Refresh()
		go func() {
			for i := range 10 {
				msg := fmt.Sprintf("This is message from another goroutine: %d", i)
				ShowPopup(myWindow, msg)
				time.Sleep(3 * time.Second)
			}

		}()
	})

	navTasks := widget.NewButton("我的任务", func() {
		rightContent.Objects = nil
		rightContent.Add(tasksContent)
		rightContent.Refresh()
	})

	// 将导航按钮放入垂直盒子
	leftNav := container.NewVBox(
		navStart,
		navTasks,
		widget.NewSeparator(),
	)

	// 创建主界面布局（左右分割）
	split := container.NewHSplit(
		container.NewBorder(nil, nil, nil, nil, leftNav),
		container.NewBorder(nil, nil, nil, nil, rightContent),
	)
	split.SetOffset(0.2) // 设置分割线位置

	// 创建背景图片（替换为你的图片路径）
	// 注意：这里使用绝对路径，实际开发中建议使用资源嵌入或相对路径
	background := canvas.NewImageFromFile("background.jpeg") // 替换为你的图片路径
	background.FillMode = canvas.ImageFillStretch

	// 将背景和原有内容叠加
	content := container.NewMax(
		background,
		split,
	)

	// 设置窗口内容并运行
	myWindow.SetContent(content)

	myWindow.ShowAndRun()
}

func ShowPopup(w fyne.Window, msg string) {
	fyne.Do(func() {
		// 创建错误提示对话框
		dialog := widget.NewModalPopUp(
			widget.NewLabel(msg),
			w.Canvas(),
		)

		dialog.Show()

		// 定时关闭
		duration := 1 * time.Second // 设置显示时间，例如3秒
		time.AfterFunc(duration, func() {
			fyne.Do(func() {
				dialog.Hide()
			})
		})
	})
}
