package global

type QrCode struct {
	Image string
	Uuid  string
}

//type Batch struct {
//	BatchNo     string
//	UserName    string
//	InvalidDate string
//	ValidCount  string
//	UseCount    string
//	ValidDay    string
//	FromName    string
//	ToName      string
//	UserEncStr  string
//}

// FavorOrderData 订单数据详情
type FavorOrderData struct {
	OrderId          string       `json:"orderId"`
	FavorClass       string       `json:"favorClass"`
	FavorClassName   string       `json:"favorClassName"`
	AccountBalance   string       `json:"accountBalance"`
	SaleTime         string       `json:"saleTime"`
	PrepayFlag       string       `json:"prepayFlag"`
	LoginName        string       `json:"loginName"`
	DbName           string       `json:"dbName"`
	NodeCode         string       `json:"nodeCode"`
	StationsId       string       `json:"stationsId"`
	StationsName     string       `json:"stationsName"`
	ReserverIdType   string       `json:"reserverIdType"`
	ReserverName     string       `json:"reserverName"`
	ReserverIdNo     string       `json:"reserverIdNo"`
	ReturnTime       string       `json:"returnTime"`
	ActiveLimitTime  string       `json:"activeLimitTime"`
	ReserveMobile    string       `json:"reserveMobile"`
	ReserveEmail     string       `json:"reserveEmail"`
	BatchList        []*BatchItem `json:"batchList"`
	OutFlagTmp       string       `json:"outFlagTmp"`
	StationsLink     string       `json:"stationsLink"`
	FavorType        string       `json:"favorType"`
	ReserverEncStr   string       `json:"reserverEncStr"`
	StartStationName string       `json:"startStationName"`
	EndStationName   string       `json:"endStationName"`
	StationsDesc     string       `json:"stationsDesc"`
	Total            int          `json:"total"`
	Sflag3           string       `json:"sflag3"`
	Sflag4           string       `json:"sflag4"`
	Sflag5           string       `json:"sflag5"`
	Sflag6           string       `json:"sflag6"`
	Sflag7           string       `json:"sflag7"`
	Sflag8           string       `json:"sflag8"`
	Sflag9           string       `json:"sflag9"`
	Sflag10          string       `json:"sflag10"`
	PlaneTicketInfo  string       `json:"planeTicketInfo"`

	Effective bool
}

// BatchItem 批次信息
type BatchItem struct {
	InvoiceStatus        string `json:"invoiceStatus"`
	ValidCount           string `json:"validCount"`
	InvalidDate          string `json:"invalidDate"`
	OutFlag              string `json:"outFlag"`
	UserIdType           string `json:"userIdType"`
	UserName             string `json:"userName"`
	UserIdNo             string `json:"userIdNo"`
	ActiveTime           string `json:"activeTime"`
	UseFlag              string `json:"useFlag"`
	UserMobile           string `json:"userMobile"`
	UserEmail            string `json:"userEmail"`
	ReturnFlag           string `json:"returnFlag"`
	TradeNoPay           string `json:"tradeNoPay"`
	BatchNo              string `json:"batchNo"`
	FavorId              string `json:"favorId"`
	FavorName            string `json:"favorName"`
	FavorType            string `json:"favorType"`
	FavorTypeDetail      string `json:"favorTypeDetail"`
	Direction            string `json:"direction"`
	FromStationTelecode  string `json:"fromStationTelecode"`
	FromStationName      string `json:"fromStationName"`
	ToStationTelecode    string `json:"toStationTelecode"`
	ToStationName        string `json:"toStationName"`
	SeatTypes            string `json:"seatTypes"`
	TicketType           string `json:"ticketType"`
	FromStations         string `json:"fromStations"`
	ToStations           string `json:"toStations"`
	UseCount             string `json:"useCount"`
	ValidDay             string `json:"validDay"`
	UserEncStr           string `json:"userEncStr"`
	TicketPrice          string `json:"ticketPrice"`
	Flag2                string `json:"flag2"`
	Flag3                string `json:"flag3"`
	Sflag4               string `json:"sflag4"`
	Sflag8               string `json:"sflag8"`
	FromStationsTelecode string `json:"fromStationsTelecode"`
	ToStationsTelecode   string `json:"toStationsTelecode"`
	FeatureLabel         string `json:"featureLabel"`
	CountryCode          string `json:"countryCode"`
	Birthday             string `json:"birthday"`
}

type Jicipiao FavorOrderData

type Agent12306 interface {
	CheckLogin() bool
	CheckLogin2() bool

	GetQrCode() (*QrCode, error)
	CheckQrCode(uuid string) (bool, error)
	InitApi() error

	QueryJicipiaos() ([]*FavorOrderData, error)
}
