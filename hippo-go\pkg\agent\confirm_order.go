package agent

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// ConfirmOrderRequest 确认订单请求参数
type ConfirmOrderRequest struct {
	OrderID      string `json:"orderId"`        // 订单ID(格式: 02#NPP2008k000000346006#P2008k0000)
	SecretStr    string `json:"secretStr"`      // 加密字符串(需要URL编码)
	OrderBatchNo string `json:"orderBatchNo"`   // 订单批次号
	SeatTypeCode string `json:"seat_type_code"` // 座位类型代码
	Passengers   string `json:"passengers"`     // 乘客信息(加密字符串)
	ChooseSeat   string `json:"choose_seat"`    // 选择的座位
	IsJy         string `json:"is_jy"`          // 是否禁运
}

func PostConfirmOrder(targetURL string, params ConfirmOrderRequest) error {
	// 准备表单数据(注意参数顺序和URL编码)
	form := url.Values{}
	form.Set("orderId", params.OrderID)
	form.Set("secretStr", params.SecretStr) // 注意：这里应该已经是双重编码的字符串
	form.Set("orderBatchNo", params.OrderBatchNo)
	form.Set("seat_type_code", params.SeatTypeCode)
	form.Set("passengers", params.Passengers)
	form.Set("choose_seat", params.ChooseSeat)
	form.Set("is_jy", params.IsJy)
	formData := form.Encode()

	req, err := http.NewRequest("POST", targetURL, strings.NewReader(formData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"application/json, text/javascript, */*; q=0.01"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {fmt.Sprintf("%d", len(formData))},
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/commutation_queryLeftTickets.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息(脱敏处理)
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debugf("请求参数: orderId=%s, batchNo=%s, seatType=%s",
		params.OrderID, params.OrderBatchNo, params.SeatTypeCode)
	log.Debugf("secretStr长度: %d bytes", len(params.SecretStr))
	log.Debugf("passengers长度: %d bytes", len(params.Passengers))

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并打印响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 尝试美化JSON输出
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, body, "", "  "); err == nil {
		log.Debugf("响应体:\n%s", prettyJSON.String())
	} else {
		log.Debugf("响应体: %s", body)
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return nil
}
