package agent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// QrCodeResponse 二维码生成响应结构
type QrCodeResponse struct {
	Image         string `json:"image"`          // Base64编码的二维码图片
	ResultMessage string `json:"result_message"` // 结果消息
	ResultCode    string `json:"result_code"`    // 结果代码
	UUID          string `json:"uuid"`           // 二维码唯一标识
}

func PostCreateQR64(targetURL string) (*QrCodeResponse, error) {
	// 准备表单数据
	form := url.Values{}
	form.Set("appid", "otn")
	formData := form.Encode()

	req, err := http.NewRequest("POST", targetURL, strings.NewReader(formData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {fmt.Sprintf("%d", len(formData))},
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/resources/login.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息
	log.Infof("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debugf("请求体: %s\n", formData)

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v\n", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var qrResp QrCodeResponse
	if err := json.Unmarshal(body, &qrResp); err != nil {
		log.Errorf("解析JSON失败: %v\n原始响应: %x", err, body)
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	log.Debugf("create qr: code=%s, msg=%s, uuid=%s, img_len=%d",
		qrResp.ResultCode, qrResp.ResultMessage, qrResp.UUID, len(qrResp.Image))

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &qrResp, nil
}
