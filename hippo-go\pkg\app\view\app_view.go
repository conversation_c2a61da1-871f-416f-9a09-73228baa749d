package view

import (
	"bytes"
	"encoding/base64"
	"image/color"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
)

type AppView struct {
	MainWindow fyne.Window

	Welcome      *fyne.Container
	RightContent *fyne.Container
	StartContent *fyne.Container
	TasksContent *fyne.Container

	// 新增Tab按钮
	StartTab *widget.Button
	TasksTab *widget.Button
}

func NewAppView(window fyne.Window) *AppView {
	rightContent := container.NewStack(
		createWelcomeView(),
	)

	// 创建Tab按钮
	startTab := widget.NewButton("开始", nil)
	tasksTab := widget.NewButton("我的任务", nil)

	return &AppView{
		MainWindow:   window,
		Welcome:      createWelcomeView(),
		RightContent: rightContent,
		StartContent: createStartView(),
		TasksContent: createTasksView(),
		StartTab:     startTab,
		TasksTab:     tasksTab,
	}
}

// 设置底部Tab栏
func (v *AppView) SetupBottomTabs() *fyne.Container {
	// 设置按钮样式
	v.StartTab.Importance = widget.HighImportance
	v.TasksTab.Importance = widget.MediumImportance

	// 创建底部Tab栏容器
	tabs := container.NewHBox(
		layout.NewSpacer(),
		v.StartTab,
		layout.NewSpacer(),
		v.TasksTab,
		layout.NewSpacer(),
	)

	// 添加背景
	bg := canvas.NewRectangle(color.NRGBA{R: 240, G: 240, B: 240, A: 255})
	tabsContainer := container.NewStack(
		bg,
		container.NewBorder(nil, nil, nil, nil, tabs),
	)

	//sContainer.(fyne.NewSize(0, 60)) // 设置Tab栏高度

	return tabsContainer
}

// 切换到指定Tab
func (v *AppView) SwitchTab(tabName string) {
	switch tabName {
	case "start":
		v.StartTab.Importance = widget.HighImportance
		v.TasksTab.Importance = widget.MediumImportance
		v.FlushContent(v.StartContent)
	case "tasks":
		v.StartTab.Importance = widget.MediumImportance
		v.TasksTab.Importance = widget.HighImportance
		v.FlushContent(v.TasksContent)
	}
}

// 创建开始页面内容
func createStartView() *fyne.Container {
	return container.NewCenter(
		widget.NewLabel("开始页面内容"),
	)
}

func (v *AppView) ShowPopup(msg string, d time.Duration) {
	// 创建错误提示对话框
	dialog := widget.NewModalPopUp(
		widget.NewLabel(msg),
		v.MainWindow.Canvas(),
	)

	dialog.Show()

	// 定时关闭
	time.AfterFunc(d, func() {
		fyne.Do(func() {
			dialog.Hide()
		})
	})
}

func (v *AppView) CreateQrView(imgStr string) *fyne.Container {
	// 解码base64图片
	imgBytes, err := base64.StdEncoding.DecodeString(imgStr)
	if err != nil {
		errmsg := "二维码解码失败"
		v.ShowPopup(errmsg, 2*time.Second)
		return nil
	}

	// 创建图片资源
	reader := bytes.NewReader(imgBytes)
	img := canvas.NewImageFromReader(reader, "qr.jpg")
	if img == nil {
		errmsg := "创建二维码图片失败"
		v.ShowPopup(errmsg, 2*time.Second)
		return nil
	}

	// 设置图片显示属性
	img.FillMode = canvas.ImageFillContain
	img.SetMinSize(fyne.NewSize(200, 200)) // 设置二维码大小

	// 创建包含提示信息和二维码的垂直布局
	msg := "请使用12306app扫码登录..."
	content := container.NewVBox(
		container.NewCenter(widget.NewLabel(msg)), // 提示信息居中
		container.NewCenter(img),                  // 二维码居中
	)

	return content
}

func (v *AppView) FlushContent(newView fyne.CanvasObject) {
	v.RightContent.RemoveAll()
	// 创建居中容器包装新视图
	v.RightContent.Add(newView)
}

func createWelcomeView() *fyne.Container {
	// 创建三行文本，每行单独设置样式
	line1 := canvas.NewText("欢迎使用Hippo通勤助手", color.White)
	line1.TextSize = 24
	line1.Alignment = fyne.TextAlignCenter

	line2 := canvas.NewText("作者: 王强", color.White)
	line2.TextSize = 18
	line2.Alignment = fyne.TextAlignCenter

	line3 := canvas.NewText("Github: https://github.com/hypnoswang", color.White)
	line3.TextSize = 16
	line3.Alignment = fyne.TextAlignCenter

	// 创建垂直排列的容器，每行文字之间有间距
	textContainer := container.NewVBox(
		container.NewCenter(line1),
		container.NewCenter(line2),
		container.NewCenter(line3),
	)

	// 添加背景（可选）
	background := canvas.NewRectangle(color.NRGBA{R: 30, G: 30, B: 40, A: 255})

	// 返回包含背景和文字的容器
	return container.NewStack(
		background,
		container.NewCenter(textContainer),
	)
}

type QrLoginHandler interface {
	HandleQrLogin()
}

func CreateQrLoginView(h QrLoginHandler) *fyne.Container {
	return container.NewCenter(
		container.NewVBox(
			container.NewHBox(
				layout.NewSpacer(),
				widget.NewButton("扫码登录", h.HandleQrLogin),
				layout.NewSpacer(),
			),
		),
	)
}

func createTasksView() *fyne.Container {
	return container.NewVBox(
		widget.NewLabel("我的任务功能待实现"),
	)
}
