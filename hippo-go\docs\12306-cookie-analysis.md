# 12306 Cookie 分析报告

## 概述

本文档详细分析了12306网站在用户访问过程中设置的各种cookies，特别是关键的`_uab_collina`cookie的生成机制和作用。

## Cookie 分析结果

### 服务端设置的Cookies

通过分析HAR文件，发现12306在整个请求过程中通过Set-Cookie响应头设置了以下cookies：

#### 1. 负载均衡相关
```http
BIGipServerotn=1540948234.24610.0000; path=/
BIGipServerpassport=*********.50215.0000; path=/
BIGipServerpool_index=*********.43286.0000; path=/
BIGipServerpool_statistics=2699100682.58176.0000; path=/
```

#### 2. 会话管理
```http
JSESSIONID=BC411DD71BE46240E441283353C095B9; Path=/index/otn
JSESSIONID=2BC5F23C52E3240E36894769BCC13159; Path=/otn
JSESSIONID=616EFA1BD94456B95B91592881A38A6A; Path=/otn
```

#### 3. 路由标识
```http
route=c5c62a339e7744272a54643b3be5bf64; Path=/
route=495c805987d0f5c8c84b14f60212447d; Path=/
```

#### 4. 认证相关
```http
_passport_session=f18bbf6aa9174a6ba22ce2b7b4a1cb8a9640; Path=/passport
uamtk=w3cv6kAisrPte2OK-iFIh5pjooYyuX4Tc33X8w45H2H0; Path=/passport
tk=7PrxjBG9VHDDCdszN1nZlBJ3_Z6KotA7O7wCCwhuH1H0; Path=/otn
uKey=a57d903613e18c104990c3b95c4bcc20b8533c0ace3e8df83a107af7fa218ea4; Path=/
```

### 客户端JavaScript设置的Cookies

#### 关键发现：`_uab_collina` Cookie

**重要发现**：`_uab_collina`cookie并非通过服务端Set-Cookie设置，而是通过JavaScript动态生成。

##### 生成时机
- 在加载`main_v30002.js`文件后
- 在发送`leftTicket/queryU`等API请求之前
- 作为反爬虫机制的一部分

##### 生成规律
```
_uab_collina=173733719108002652645464
```

分析cookie值结构：
- **前13位**: `1737337191080` - 时间戳（毫秒）
- **后11位**: `02652645464` - 随机数或设备指纹

##### 证据链
1. **第一次加载** `main_v30002.js` 时：cookie中没有`_uab_collina`
2. **后续API请求**时：cookie中出现了`_uab_collina`
3. **调用栈显示**：由`main_v30002.js`中的JavaScript代码设置

## 其他客户端设置的Cookies

通过分析发现，以下cookies也是通过JavaScript设置的：

```http
guidesStatus=off
highContrastMode=defaltMode
cursorStatus=off
_jc_save_wfdc_flag=dc
_jc_save_fromStation=%u5317%u4EAC%2CBJP  // 北京,BJP
_jc_save_toStation=%u6210%u90FD%2CCDW    // 成都,CDW
```

## 对hippo-go项目的影响

### 必须模拟的Cookies

1. **`_uab_collina`** - 反爬虫关键cookie
2. **`guidesStatus`**, **`highContrastMode`**, **`cursorStatus`** - 用户界面状态
3. **`_jc_save_wfdc_flag`** - 网络检测标志
4. **`_jc_save_fromStation`**, **`_jc_save_toStation`** - 车站选择记忆

### 实现建议

#### 1. `_uab_collina`生成算法
```go
func generateUabCollina() string {
    // 生成13位时间戳（毫秒）
    timestamp := time.Now().UnixNano() / 1000000
    
    // 生成11位随机数
    rand.Seed(time.Now().UnixNano())
    suffix := rand.Int63n(99999999999)
    
    return fmt.Sprintf("%d%011d", timestamp, suffix)
}
```

#### 2. 设置时机
- 在发送API请求前动态生成
- 特别是`leftTicket/queryU`和`queryFavorTrainDefine`等关键API

#### 3. 车站信息Cookies
```go
func setStationCookies(fromName, fromCode, toName, toCode string) {
    fromStation := fmt.Sprintf("%s,%s", fromName, fromCode)
    toStation := fmt.Sprintf("%s,%s", toName, toCode)
    
    // URL编码
    encodedFrom := url.QueryEscape(fromStation)
    encodedTo := url.QueryEscape(toStation)
}
```

## 总结

1. **服务端cookies**主要用于会话管理、负载均衡和用户认证
2. **客户端cookies**主要用于反爬虫、用户体验和状态记忆
3. **`_uab_collina`**是关键的反爬虫cookie，必须正确模拟
4. **时机很重要**：必须在正确的时间点设置相应的cookies

## 参考资料

- HAR文件分析：`kyfw.12306.cn-5.har`
- 关键JavaScript文件：`main_v30002.js`
- API调用链：`queryFavorTrainDefine` → `leftTicket/queryU`
