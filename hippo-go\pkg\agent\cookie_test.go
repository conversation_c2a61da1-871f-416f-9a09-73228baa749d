package agent

import (
	"fmt"
	"net/http"
	"regexp"
	"testing"
)

// TestGenerateUabCollina 测试_uab_collina cookie生成
func TestGenerateUabCollina(t *testing.T) {
	// 生成多个cookie值进行测试
	for i := 0; i < 5; i++ {
		collina := generateUabCollina()
		
		// 验证格式：应该是24位数字
		if len(collina) != 24 {
			t.<PERSON><PERSON><PERSON>("_uab_collina长度错误，期望24位，实际%d位: %s", len(collina), collina)
		}
		
		// 验证是否全为数字
		matched, _ := regexp.MatchString(`^\d{24}$`, collina)
		if !matched {
			t.Errorf("_uab_collina格式错误，应该全为数字: %s", collina)
		}
		
		fmt.Printf("生成的_uab_collina: %s\n", collina)
	}
}

// TestGenerateRequiredCookies 测试基础cookies生成
func TestGenerateRequiredCookies(t *testing.T) {
	cookies := GenerateRequiredCookies()
	
	// 验证必需的cookies是否存在
	requiredCookies := []string{
		"_uab_collina",
		"_jc_save_wfdc_flag",
		"guidesStatus",
		"highContrastMode",
		"cursorStatus",
	}
	
	for _, cookieName := range requiredCookies {
		if value, exists := cookies[cookieName]; !exists {
			t.Errorf("缺少必需的cookie: %s", cookieName)
		} else {
			fmt.Printf("Cookie %s: %s\n", cookieName, value)
		}
	}
}

// TestSetStationCookies 测试车站cookies生成
func TestSetStationCookies(t *testing.T) {
	cookies := SetStationCookies("北京", "BJP", "成都", "CDW")
	
	// 验证车站cookies
	if fromStation, exists := cookies["_jc_save_fromStation"]; !exists {
		t.Error("缺少_jc_save_fromStation cookie")
	} else {
		fmt.Printf("_jc_save_fromStation: %s\n", fromStation)
	}
	
	if toStation, exists := cookies["_jc_save_toStation"]; !exists {
		t.Error("缺少_jc_save_toStation cookie")
	} else {
		fmt.Printf("_jc_save_toStation: %s\n", toStation)
	}
}

// TestSetupUabCollinaCookie 测试HTTP请求cookie设置
func TestSetupUabCollinaCookie(t *testing.T) {
	req, err := http.NewRequest("GET", "https://example.com", nil)
	if err != nil {
		t.Fatalf("创建请求失败: %v", err)
	}
	
	// 设置_uab_collina cookie
	SetupUabCollinaCookie(req)
	
	// 验证cookie是否被正确设置
	cookies := req.Cookies()
	found := false
	for _, cookie := range cookies {
		if cookie.Name == "_uab_collina" {
			found = true
			fmt.Printf("HTTP请求中的_uab_collina: %s\n", cookie.Value)
			
			// 验证格式
			if len(cookie.Value) != 24 {
				t.Errorf("HTTP请求中的_uab_collina长度错误: %d", len(cookie.Value))
			}
			break
		}
	}
	
	if !found {
		t.Error("HTTP请求中未找到_uab_collina cookie")
	}
}
