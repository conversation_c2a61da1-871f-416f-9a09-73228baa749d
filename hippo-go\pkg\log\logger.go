package log

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	lumberjack "gopkg.in/natefinch/lumberjack.v2"
)

func NewLogger(logPath string, level Level) *zap.Logger {
	// 配置日志轮转
	hook := lumberjack.Logger{
		Filename:   logPath, // 日志文件路径
		MaxSize:    128,     // 每个日志文件最大尺寸(MB)
		MaxBackups: 30,      // 保留旧文件最大数量
		MaxAge:     7,       // 保留旧文件最大天数
		Compress:   true,    // 是否压缩旧文件
		LocalTime:  true,    // 使用本地时间命名
	}

	// 日志级别设置
	atomicLevel := zap.NewAtomicLevel()
	atomicLevel.SetLevel(levelToZapLevel(level)) // 设置日志级别

	// 编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseColorLevelEncoder, // 带颜色的级别
		EncodeTime:     zapcore.ISO8601TimeEncoder,         // ISO8601 时间格式
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder, // 短路径调用者
	}

	// 多输出端（文件 + 控制台）
	core := zapcore.NewTee(
		// 输出到文件
		zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),
			zapcore.AddSync(&hook),
			atomicLevel,
		),
		// 同时输出到控制台
		zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig),
			zapcore.AddSync(os.Stdout),
			atomicLevel,
		),
	)

	// 构造Logger
	logger := zap.New(core,
		zap.AddCaller(),
		zap.AddStacktrace(zapcore.PanicLevel), // Error及以上级别记录堆栈
	)

	defer logger.Sync()

	return logger
}

func levelToZapLevel(level Level) zapcore.Level {
	switch level {
	case LevelDebug:
		return zapcore.DebugLevel
	case LevelInfo:
		return zapcore.InfoLevel
	case LevelWarn:
		return zapcore.WarnLevel
	case LevelError:
		return zapcore.ErrorLevel
	case LevelPanic:
		return zapcore.PanicLevel
	case LevelDPanic:
		return zapcore.DPanicLevel
	case LevelFatal:
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}
