package view

import (
	"fmt"
	"image/color"
	"strconv"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"

	"github.com/hypnoswang/hippo-go/pkg/global"
)

func (v *AppView) CreateJicipiaoView(jicipiaos []*global.FavorOrderData, handler func(*global.FavorOrderData, *global.BatchItem)) fyne.CanvasObject {
	// 准备数据
	data, batchRefs, favorRefs := v.prepareCardData(jicipiaos)

	// 创建卡片容器
	cardContainer := container.NewVBox()

	for i, cardData := range data {
		// 创建单个卡片
		card := v.createJicipiaoCard(cardData, batchRefs[i], favorRefs[i], handler)
		cardContainer.Add(card)

		// 添加分隔线（最后一个卡片不加）
		if i < len(data)-1 {
			cardContainer.Add(widget.NewSeparator())
		}
	}

	// 返回可滚动的卡片列表
	return container.NewScroll(cardContainer)
}

// 创建单个计次票卡片
func (v *AppView) createJicipiaoCard(data *cardData, batch *global.BatchItem, favor *global.FavorOrderData, handler func(*global.FavorOrderData, *global.BatchItem)) fyne.CanvasObject {
	// 1. 创建可交换的车站标签
	fromLabel := widget.NewLabel(data.fromStation)
	toLabel := widget.NewLabel(data.toStation)

	// 2. 创建可点击的箭头按钮
	arrowBtn := widget.NewButton("→", nil)
	arrowBtn.Importance = widget.LowImportance
	arrowBtn.OnTapped = func() {
		// 交换车站名称
		fromText := fromLabel.Text
		fromLabel.SetText(toLabel.Text)
		toLabel.SetText(fromText)
	}

	// 3. 车站信息行
	stationRow := container.NewHBox(
		fromLabel,
		layout.NewSpacer(),
		arrowBtn,
		layout.NewSpacer(),
		toLabel,
	)
	stationRow.Layout = layout.NewHBoxLayout() // 确保紧凑布局

	// 4. 计次信息行
	usageInfo := widget.NewLabel(data.usageInfo)
	remainingInfo := widget.NewLabel(data.remainingInfo)
	validDateInfo := widget.NewLabel(data.validDateInfo)
	infoRow := container.NewHBox(
		usageInfo,
		layout.NewSpacer(),
		remainingInfo,
		layout.NewSpacer(),
		validDateInfo,
	)

	// 5. 操作按钮（如果有剩余次数）
	var actionBtn *widget.Button
	if data.hasAction {
		actionBtn = widget.NewButton("预订", func() {
			handler(favor, batch)
		})
		actionBtn.Importance = widget.HighImportance
	}

	// 6. 卡片内容布局
	content := container.NewVBox(
		container.NewPadded(stationRow), // 添加内边距使布局更美观
		infoRow,
	)

	if actionBtn != nil {
		content.Add(container.NewCenter(actionBtn))
	}

	// 7. 添加卡片边框和背景
	card := container.NewStack(
		canvas.NewRectangle(color.NRGBA{R: 245, G: 245, B: 245, A: 255}), // 浅灰色背景
		container.NewVBox(
			container.NewPadded(content),
		),
	)

	// 设置卡片内边距
	card = container.NewPadded(card)

	return card
}

// 卡片数据结构
type cardData struct {
	fromStation   string
	toStation     string
	usageInfo     string
	remainingInfo string
	validDateInfo string
	hasAction     bool
}

// 准备卡片数据
func (v *AppView) prepareCardData(jicipiaos []*global.FavorOrderData) ([]*cardData, []*global.BatchItem, []*global.FavorOrderData) {
	var data []*cardData
	var batchRefs []*global.BatchItem
	var favorRefs []*global.FavorOrderData

	for _, jp := range jicipiaos {
		if !jp.Effective {
			continue
		}

		for _, batch := range jp.BatchList {
			validCount, _ := strconv.Atoi(batch.ValidCount)
			useCount, _ := strconv.Atoi(batch.UseCount)
			validDay := batch.ValidDay
			if validDay == "" {
				validDay = "∞"
			}

			// 创建卡片数据
			card := &cardData{
				fromStation:   batch.FromStationName,
				toStation:     batch.ToStationName,
				usageInfo:     fmt.Sprintf("%d次/%s天", useCount, validDay),
				remainingInfo: fmt.Sprintf("剩余%d次", validCount),
				validDateInfo: fmt.Sprintf("有效期至%s", batch.InvalidDate), // 有效期信息，可根据需要修改格式
				hasAction:     validCount > 0,
			}

			data = append(data, card)
			batchRefs = append(batchRefs, batch)
			favorRefs = append(favorRefs, jp)
		}
	}

	return data, batchRefs, favorRefs
}
