package agent

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

func PostUAMTKStatic(targetURL string) error {
	form := url.Values{}
	form.Set("appid", "otn")
	formData := form.Encode()

	req, err := http.NewRequest("POST", targetURL, strings.NewReader(formData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {fmt.Sprintf("%d", len(formData))}, // 自动计算长度
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://www.12306.cn"},
		"Referer":            {"https://www.12306.cn/"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-site"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
	}

	// 打印请求Cookies
	PrintRequestCookies(req)

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}

	// 处理响应
	body, _ := utils.DecompressResponse(resp)
	log.Infof("\n响应状态: %s\n", resp.Status)

	// 打印响应后的Cookies
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	log.Debugf("响应体: %s\n", body)

	return nil
}
