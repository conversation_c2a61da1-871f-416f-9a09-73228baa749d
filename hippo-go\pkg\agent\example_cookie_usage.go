package agent

import (
	"fmt"
	"net/http"
)

// ExampleCookieUsage 展示如何使用新的cookie生成功能
func ExampleCookieUsage() {
	fmt.Println("=== 12306 Cookie 生成示例 ===")
	
	// 1. 生成基础cookies
	fmt.Println("\n1. 生成基础cookies:")
	baseCookies := GenerateRequiredCookies()
	for name, value := range baseCookies {
		fmt.Printf("  %s: %s\n", name, value)
	}
	
	// 2. 生成车站cookies
	fmt.Println("\n2. 生成车站cookies:")
	stationCookies := SetStationCookies("北京", "BJP", "成都", "CDW")
	for name, value := range stationCookies {
		fmt.Printf("  %s: %s\n", name, value)
	}
	
	// 3. 为HTTP请求设置cookies
	fmt.Println("\n3. 为HTTP请求设置cookies:")
	req, _ := http.NewRequest("GET", "https://kyfw.12306.cn/otn/leftTicket/queryU", nil)
	
	// 设置关键的_uab_collina cookie
	SetupUabCollinaCookie(req)
	
	// 设置其他cookies
	SetupAdditionalCookies(req, "北京", "BJP", "成都", "CDW")
	
	// 显示请求中的所有cookies
	fmt.Println("  HTTP请求中的cookies:")
	for _, cookie := range req.Cookies() {
		fmt.Printf("    %s: %s\n", cookie.Name, cookie.Value)
	}
	
	fmt.Println("\n=== 示例完成 ===")
}
