package log

type Level int32

const (
	LevelNone Level = iota
	LevelDebug
	LevelInfo
	LevelWarn
	LevelError
	LevelPanic
	LevelDPanic
	LevelFatal
)

var defaultLogger = NewLogger("./logs/hippo.log", LevelDebug)

//func Debug(v ...any) {
//	defaultLogger.Sugar().Debug(v...)
//}
//
//func Debugf(format string, v ...any) {
//	defaultLogger.Sugar().Debugf(format, v...)
//}
//
//func Info(v ...any) {
//	defaultLogger.Sugar().Info(v...)
//}
//
//func Infof(format string, v ...any) {
//	defaultLogger.Sugar().Infof(format, v...)
//}
//
//func Warn(v ...any) {
//	defaultLogger.Sugar().Warn(v...)
//}
//
//func Warnf(format string, v ...any) {
//	defaultLogger.Sugar().Warnf(format, v...)
//}
//
//func Error(v ...any) {
//	defaultLogger.Sugar().Warn(v...)
//}
//
//func Errorf(format string, v ...any) {
//	defaultLogger.Sugar().Errorf(format, v...)
//}
//
//func Panic(v ...any) {
//	defaultLogger.Sugar().Panic(v...)
//}
//
//func Panicf(format string, v ...any) {
//	defaultLogger.Sugar().Panicf(format, v...)
//}
//
//func DPanic(v ...any) {
//	defaultLogger.Sugar().DPanic(v...)
//}
//
//func DPanicf(format string, v ...any) {
//	defaultLogger.Sugar().DPanicf(format, v...)
//}
//
//func Fatal(v ...any) {
//	defaultLogger.Sugar().Fatal(v...)
//}
//
//func Fatalf(format string, v ...any) {
//	defaultLogger.Sugar().Fatalf(format, v...)
//}

var (
	Debug   = defaultLogger.Sugar().Debug
	Debugf  = defaultLogger.Sugar().Debugf
	Info    = defaultLogger.Sugar().Info
	Infof   = defaultLogger.Sugar().Infof
	Warn    = defaultLogger.Sugar().Warn
	Warnf   = defaultLogger.Sugar().Warnf
	Error   = defaultLogger.Sugar().Error
	Errorf  = defaultLogger.Sugar().Errorf
	Panic   = defaultLogger.Sugar().Panic
	Panicf  = defaultLogger.Sugar().Panicf
	DPanic  = defaultLogger.Sugar().DPanic
	DPanicf = defaultLogger.Sugar().DPanicf
	Fatal   = defaultLogger.Sugar().Fatal
	Fatalf  = defaultLogger.Sugar().Fatalf
)
