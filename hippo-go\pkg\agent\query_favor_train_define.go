package agent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// FavorTrainDefineRequest 查询参数结构体
type FavorTrainDefineRequest struct {
	FavorClass          string `json:"favorClass"`          // 票种类型
	FavorId             string `json:"favorId"`             // 票种ID
	TrainDate           string `json:"trainDate"`           // 乘车日期(yyyyMMdd)
	FromStationTelecode string `json:"fromStationTelecode"` // 出发站电报码
	ToStationTelecode   string `json:"toStationTelecode"`   // 到达站电报码
}

// FavorTrainDefineResponse 响应结构
type FavorTrainDefineResponse struct {
	ValidateMessagesShowId string                 `json:"validateMessagesShowId"`
	Status                 bool                   `json:"status"`
	HttpStatus             int                    `json:"httpstatus"`
	Data                   FavorTrainDefineData   `json:"data"`
	Messages               []interface{}          `json:"messages"`
	ValidateMessages       map[string]interface{} `json:"validateMessages"`
}

// FavorTrainDefineData 车次定义数据
type FavorTrainDefineData struct {
	FromStations  string `json:"fromStations"`  // 出发站列表
	ToStations    string `json:"toStations"`    // 到达站列表
	UsableTrain   string `json:"usableTrain"`   // 可用车次
	UnusableTrain string `json:"unusableTrain"` // 不可用车次
}

func PostQueryFavorTrainDefine(targetURL string, params FavorTrainDefineRequest) (*FavorTrainDefineResponse, error) {
	// 准备表单数据
	form := url.Values{}
	form.Set("favorClass", params.FavorClass)
	form.Set("favorId", params.FavorId)
	form.Set("trainDate", params.TrainDate)
	form.Set("fromStationTelecode", params.FromStationTelecode)
	form.Set("toStationTelecode", params.ToStationTelecode)
	formData := form.Encode()

	req, err := http.NewRequest("POST", targetURL, strings.NewReader(formData))
	if err != nil {
		log.Errorf("创建请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置关键的_uab_collina cookie（反爬虫机制）
	// 根据HAR分析，这个cookie必须在API请求前动态生成
	SetupUabCollinaCookie(req)

	// 设置其他额外的cookies（模拟浏览器行为）
	SetupAdditionalCookies(req, "出发站", params.FromStationTelecode, "到达站", params.ToStationTelecode)

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"application/json, text/javascript, */*; q=0.01"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Content-Length":     {fmt.Sprintf("%d", len(formData))},
		"Content-Type":       {"application/x-www-form-urlencoded; charset=UTF-8"},
		"Host":               {"kyfw.12306.cn"},
		"Origin":             {"https://kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/commutation_queryLeftTickets.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   {"XMLHttpRequest"},
	}

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debugf("请求参数: %+v", params)
	log.Debugf("请求体: %s", formData)

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	log.Debugf("响应体: %s", body)

	var trainResp FavorTrainDefineResponse
	if err := json.Unmarshal(body, &trainResp); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v\n原始响应: %s", err, body)
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &trainResp, nil
}

// ParseUsableTrains 解析可用车次列表
func (d *FavorTrainDefineData) ParseUsableTrains() []string {
	return strings.Split(strings.TrimSuffix(d.UsableTrain, "#"), "#")
}

// ParseFromStations 解析出发站列表
func (d *FavorTrainDefineData) ParseFromStations() []string {
	return strings.Split(strings.TrimSuffix(d.FromStations, "#"), "#")
}

// ParseToStations 解析到达站列表
func (d *FavorTrainDefineData) ParseToStations() []string {
	return strings.Split(strings.TrimSuffix(d.ToStations, "#"), "#")
}
