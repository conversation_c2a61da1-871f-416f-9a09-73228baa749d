package agent

import (
	"crypto/tls"
	"net/http"

	//"net/http/cookiejar"
	"net/url"

	"github.com/hypnoswang/hippo-go/pkg/log"
	cookiejar "github.com/juju/persistent-cookiejar"
)

func NewInsecureClient() *http.Client {
	jar, _ := cookiejar.New(&cookiejar.Options{Filename: "mycookies"})
	return &http.Client{
		Jar: jar,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
}

// 使用时创建新的client实例
var httpClient = NewInsecureClient()

// PrintRequestCookies 打印请求将要携带的Cookies
func PrintRequestCookies(req *http.Request) {
	log.Debug("===== 请求携带的Cookies =====")
	if req == nil {
		log.Error("  (无请求信息)")
		return
	}

	cookies := req.Cookies()
	if len(cookies) == 0 {
		log.Error("  (无Cookies)")
		return
	}

	for _, cookie := range cookies {
		log.Debugf("  %s=%s (Domain:%s Path:%s Raw:%s)\n",
			cookie.Name,
			cookie.Value,
			cookie.Domain,
			cookie.Path,
			cookie.Raw)
	}
}

// PrintCurrentCookies 打印当前Jar中的所有Cookies
func PrintCurrentCookies(u *url.URL) {
	log.Debugf("===== 当前存储的Cookies =====")
	if u == nil {
		log.Debugf("  (无URL信息)")
		return
	}

	cookies := httpClient.Jar.Cookies(u)
	if len(cookies) == 0 {
		log.Debugf("  (无Cookies)")
		return
	}

	for _, cookie := range cookies {
		log.Debugf("  %s=%s (Domain:%s Expires:%v HttpOnly:%v Secure:%v)\n",
			cookie.Name,
			cookie.Value,
			cookie.Domain,
			cookie.Expires,
			cookie.HttpOnly,
			cookie.Secure)
	}
}
