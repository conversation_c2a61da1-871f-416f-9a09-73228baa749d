# 12306 HAR 文件分析报告

> 基于 `kyfw.12306.cn-5.har` 文件的详细分析报告  
> 生成时间：2025-06-28  
> 分析文件：kyfw.12306.cn-5.har

## 📊 概述

本报告通过分析真实的浏览器访问 12306 网站的 HAR 文件，提取了完整的登录和票务查询流程，为 hippo-go 项目的改进提供了详细的技术参考。

## 🔍 关键发现

- ✅ 完整的二维码登录流程
- ✅ 详细的 API 接口调用序列
- ✅ 必需的请求头和 Cookie 配置
- ✅ 票务查询的完整参数结构
- ⚠️ 发现了项目中的安全风险和改进点

## 🚀 核心 API 流程分析

### 1. 二维码登录流程

#### 步骤 1：创建二维码
```http
POST https://kyfw.12306.cn/passport/web/create-qr64
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

appid=otn
```

**响应示例：**
```json
{
  "image": "iVBORw0KGgoAAAANSUhEUgAAAMcAAADH...",
  "result_message": "生成二维码成功",
  "result_code": "0",
  "uuid": "swJbLYv1mjSNT7npP971fCIMfGcEaoScAPTYXmVnWAWKg3lWyAu573AWCT4dk_wF4eVGJNkTgYm8ty1"
}
```

#### 步骤 2：轮询检查二维码状态
```http
POST https://kyfw.12306.cn/passport/web/checkqr
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

uuid=<二维码UUID>&appid=otn
```

**状态码含义：**
- `0`: 未识别
- `1`: 已识别，暂未授权（未点击授权或不授权）
- `2`: 登录成功（已识别且已授权）
- `3`: 已失效
- `5`: 系统异常

**轮询策略：** 每 1 秒检查一次，直到状态为 `2` 或 `3`

### 2. 登录配置获取

```http
POST https://kyfw.12306.cn/otn/login/conf
```

**关键配置项：**
- `is_uam_login`: 是否统一认证登录
- `is_sweep_login`: 是否开启扫码登录
- `is_login`: 是否已登录
- `is_login_passCode`: 是否启用验证码校验登录
- `is_message_passCode`: 是否显示短信验证

### 3. Token 获取流程

```http
POST https://kyfw.12306.cn/passport/web/auth/uamtk-static
Content-Type: application/x-www-form-urlencoded

appid=otn
```

**作用：** 获取认证 Token，用于后续 API 调用

## 🎫 票务查询 API

### 1. 定期票查询

```http
POST https://kyfw.12306.cn/otn/npQuery/queryFavorTrainDefine
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

favorClass=P200E&favorId=P2009i0000&trainDate=20250612&fromStationTelecode=WWP&toStationTelecode=VNP
```

**参数说明：**
- `favorClass`: 定期票类别
- `favorId`: 定期票 ID
- `trainDate`: 乘车日期（YYYYMMDD 格式）
- `fromStationTelecode`: 出发站电报码
- `toStationTelecode`: 到达站电报码

### 2. 余票查询

```http
GET https://kyfw.12306.cn/otn/leftTicket/queryU?leftTicketDTO.train_date=2025-06-12&leftTicketDTO.from_station=WWP&leftTicketDTO.to_station=VNP&purpose_codes=ADULT
```

**参数说明：**
- `leftTicketDTO.train_date`: 乘车日期（YYYY-MM-DD 格式）
- `leftTicketDTO.from_station`: 出发站代码
- `leftTicketDTO.to_station`: 到达站代码
- `purpose_codes`: 乘客类型（ADULT=成人）

## 🍪 关键 Cookie 分析

### 必需的 Cookie

| Cookie 名称 | 作用 | 示例值 |
|------------|------|--------|
| `_passport_session` | 认证会话标识 | f18bbf6aa9174a6ba22ce2b7b4a1cb8a9640 |
| `BIGipServerotn` | OTN 服务器负载均衡 | 1540948234.24610.0000 |
| `BIGipServerpassport` | 认证服务器负载均衡 | *********.50215.0000 |
| `tk` | 认证 Token | 7PrxjBG9VHDDCdszN1nZlBJ3_Z6KotA7O7wCCwhuH1H0 |
| `uKey` | 用户密钥 | a57d903613e18c104990c3b95c4bcc20b8533c0ace3e8df83a107af7fa218ea4 |
| `JSESSIONID` | Java 会话 ID | 616EFA1BD94456B95B91592881A38A6A |
| `guidesStatus` | 引导状态 | off |
| `highContrastMode` | 高对比度模式 | defaltMode |
| `cursorStatus` | 光标状态 | off |
| `route` | 路由标识 | 495c805987d0f5c8c84b14f60212447d |

## 📋 标准请求头

### 必需的请求头
```http
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
Accept: application/json, text/javascript, */*; q=0.01
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
X-Requested-With: XMLHttpRequest
Referer: https://kyfw.12306.cn/otn/resources/login.html
Origin: https://kyfw.12306.cn
Host: kyfw.12306.cn
Connection: keep-alive
```

### 安全相关请求头
```http
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
sec-ch-ua: "Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

## 🔧 对 hippo-go 项目的改进建议

### 1. API 接口完善

**当前缺少的关键接口：**

```go
// 建议在 pkg/agent/const.go 中添加
const (
    // 登录相关
    URL_LOGIN_CONF = "/otn/login/conf"
    URL_UAMTK_STATIC = "/passport/web/auth/uamtk-static"
    URL_UAMTK = "/passport/web/auth/uamtk"
    
    // 票务查询
    URL_QUERY_FAVOR_DEFINE = "/otn/npQuery/queryFavorTrainDefine"
    URL_LEFT_TICKET_QUERY = "/otn/leftTicket/queryU"
    
    // 其他
    URL_UAMAUTHCLIENT = "/otn/uamauthclient"
)
```

### 2. Cookie 管理优化

**建议改进 Cookie 管理：**

```go
// 在 agent.go 中添加
func (a *agent12306) initRequiredCookies() {
    cookies := []*http.Cookie{
        {Name: "guidesStatus", Value: "off", Domain: ".12306.cn"},
        {Name: "highContrastMode", Value: "defaltMode", Domain: ".12306.cn"},
        {Name: "cursorStatus", Value: "off", Domain: ".12306.cn"},
    }
    
    for _, cookie := range cookies {
        a.cookieJar.SetCookies(a.baseURL, []*http.Cookie{cookie})
    }
}
```

### 3. 请求头标准化

**建议统一请求头设置：**

```go
func (a *agent12306) setStandardHeaders(req *http.Request) {
    req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
    req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
    req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
    req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5")
    req.Header.Set("X-Requested-With", "XMLHttpRequest")
    req.Header.Set("Referer", "https://kyfw.12306.cn/otn/resources/login.html")
    req.Header.Set("Origin", "https://kyfw.12306.cn")
    req.Header.Set("sec-ch-ua", `"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`)
    req.Header.Set("sec-ch-ua-mobile", "?0")
    req.Header.Set("sec-ch-ua-platform", `"Windows"`)
    req.Header.Set("Sec-Fetch-Dest", "empty")
    req.Header.Set("Sec-Fetch-Mode", "cors")
    req.Header.Set("Sec-Fetch-Site", "same-origin")
}
```

### 4. 错误处理改进

**根据 HAR 分析，建议添加更详细的状态码处理：**

```go
type QrCheckResult struct {
    ResultCode    string `json:"result_code"`
    ResultMessage string `json:"result_message"`
}

func (a *agent12306) handleQrCheckResult(result *QrCheckResult) error {
    switch result.ResultCode {
    case "0": // 未识别
        return nil
    case "1": // 已识别，暂未授权
        log.Info("二维码已识别，等待用户授权...")
        return nil
    case "2": // 登录成功
        log.Info("登录成功！")
        return a.handleLoginSuccess()
    case "3": // 已失效
        return errors.New("二维码已失效，请重新获取")
    case "5": // 系统异常
        return errors.New("系统异常，请稍后重试")
    default:
        return fmt.Errorf("未知状态码: %s", result.ResultCode)
    }
}
```

## 📈 性能优化建议

### 1. 连接复用
- HAR 显示使用了 HTTP/1.1 keep-alive
- 建议优化 HTTP 客户端连接池配置
- 设置合适的连接超时和空闲超时

### 2. 压缩支持
- 确保支持 gzip、deflate、br 压缩
- 可以显著减少网络传输量

### 3. 请求间隔控制
- 二维码检查建议 1 秒间隔
- 避免过于频繁的请求被识别为异常行为

## 🔒 安全建议

### 1. 移除安全风险
```go
// ❌ 当前代码中的安全风险
Transport: &http.Transport{
    TLSClientConfig: &tls.Config{
        InsecureSkipVerify: true, // 这是严重的安全风险！
    },
}

// ✅ 建议的安全配置
Transport: &http.Transport{
    TLSClientConfig: &tls.Config{
        InsecureSkipVerify: false, // 启用证书验证
        MinVersion:         tls.VersionTLS12,
    },
}
```

### 2. 反爬虫对策
- 添加请求签名机制
- User-Agent 轮换策略
- 请求频率控制
- 添加随机延迟

### 3. 数据验证
- 对所有输入数据进行严格验证
- 添加 SQL 注入和 XSS 防护
- 敏感信息加密存储

## 📝 实施优先级

### 高优先级 🔴
1. **移除 `InsecureSkipVerify`** - 严重安全风险
2. **完善请求头设置** - 提高成功率
3. **优化 Cookie 管理** - 确保会话正常

### 中优先级 🟡
1. **添加缺失的 API 接口** - 功能完整性
2. **改进错误处理** - 用户体验
3. **标准化配置管理** - 代码质量

### 低优先级 🟢
1. **性能优化** - 响应速度
2. **添加单元测试** - 代码质量
3. **文档完善** - 维护性

## 📚 参考资料

- [12306 官方网站](https://www.12306.cn/)
- [HTTP Archive (HAR) 格式规范](https://w3c.github.io/web-performance/specs/HAR/Overview.html)
- [Go HTTP 客户端最佳实践](https://golang.org/pkg/net/http/)

---

**注意：** 本分析报告基于真实的浏览器访问数据，但 12306 网站可能会更新其 API 和安全策略。建议定期更新分析数据，确保项目的持续有效性。
