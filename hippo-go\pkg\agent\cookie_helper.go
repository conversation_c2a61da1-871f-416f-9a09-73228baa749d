package agent

import (
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"time"
)

// generateUabCollina 生成_uab_collina cookie值
// 根据HAR文件分析，格式为：13位时间戳 + 11位随机
func generateUabCollina() string {
	// 生成13位时间戳（毫秒）
	timestamp := time.Now().UnixNano() / 1000000

	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	// 生成11位随机数
	// rand.Seed(time.Now().UnixNano())
	// suffix := rand.Int63n(99999999999) // 11位随机数 (0-99999999999)
	suffix := rd.Int63n(99999999999)

	return fmt.Sprintf("%d%011d", timestamp, suffix)
}

// GenerateRequiredCookies 生成12306需要的额外cookies
func GenerateRequiredCookies() map[string]string {
	cookies := make(map[string]string)

	// 1. 生成 _uab_collina (用户行为分析ID)
	// 这是关键的反爬虫cookie，必须在API请求前动态生成
	cookies["_uab_collina"] = generateUabCollina()

	// 2. 网络检测标志
	cookies["_jc_save_wfdc_flag"] = "dc"

	// 3. 用户界面相关（模拟正常浏览器状态）
	cookies["guidesStatus"] = "off"
	cookies["highContrastMode"] = "defaltMode"
	cookies["cursorStatus"] = "off"

	return cookies
}

// SetStationCookies 设置车站选择相关的cookies
func SetStationCookies(fromStationName, fromStationCode, toStationName, toStationCode string) map[string]string {
	cookies := make(map[string]string)

	// URL编码站点信息
	fromStation := fmt.Sprintf("%s,%s", fromStationName, fromStationCode)
	toStation := fmt.Sprintf("%s,%s", toStationName, toStationCode)

	// 使用Unicode编码（12306的特殊编码方式）
	cookies["_jc_save_fromStation"] = url.QueryEscape(fromStation)
	cookies["_jc_save_toStation"] = url.QueryEscape(toStation)

	return cookies
}

// SetupAdditionalCookies 为请求设置额外的cookies
func SetupAdditionalCookies(req *http.Request, fromStationName, fromStationCode, toStationName, toStationCode string) {
	// 获取基础cookies（包含新生成的_uab_collina）
	baseCookies := GenerateRequiredCookies()

	// 获取车站cookies
	stationCookies := SetStationCookies(fromStationName, fromStationCode, toStationName, toStationCode)

	// 合并所有cookies
	allCookies := make(map[string]string)
	for k, v := range baseCookies {
		allCookies[k] = v
	}
	for k, v := range stationCookies {
		allCookies[k] = v
	}

	// 设置到请求中
	for name, value := range allCookies {
		cookie := &http.Cookie{
			Name:  name,
			Value: value,
		}
		req.AddCookie(cookie)
	}
}

// SetupUabCollinaCookie 专门为API请求设置_uab_collina cookie
// 根据HAR分析，这个cookie必须在发送leftTicket/queryU等API请求前动态生成
func SetupUabCollinaCookie(req *http.Request) {
	uabCollina := generateUabCollina()
	cookie := &http.Cookie{
		Name:  "_uab_collina",
		Value: uabCollina,
	}
	req.AddCookie(cookie)
}
