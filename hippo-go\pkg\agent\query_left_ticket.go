package agent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// LeftTicketQueryParams 查询参数结构体
type LeftTicketQueryParams struct {
	TrainDate   string `json:"train_date"`    // 乘车日期(yyyy-MM-dd)
	FromStation string `json:"from_station"`  // 出发站电报码
	ToStation   string `json:"to_station"`    // 到达站电报码
	PurposeCode string `json:"purpose_codes"` // 票种(ADULT/STUDENT等)
}

// LeftTicketResponse 余票查询响应结构
type LeftTicketResponse struct {
	HttpStatus int            `json:"httpstatus"`
	Data       LeftTicketData `json:"data"`
	Messages   string         `json:"messages"`
	Status     bool           `json:"status"`
}

// LeftTicketData 余票数据
type LeftTicketData struct {
	Result     []string          `json:"result"`  // 车次结果列表
	Flag       string            `json:"flag"`    // 查询标志
	Level      string            `json:"level"`   // 查询级别
	SameTLC    string            `json:"sametlc"` // 同城标志
	StationMap map[string]string `json:"map"`     // 车站映射表
}

func GetLeftTicketList(targetURL string, params LeftTicketQueryParams) (*LeftTicketResponse, error) {
	// 构造查询参数
	query := url.Values{}
	query.Set("leftTicketDTO.train_date", params.TrainDate)
	query.Set("leftTicketDTO.from_station", params.FromStation)
	query.Set("leftTicketDTO.to_station", params.ToStation)
	query.Set("purpose_codes", params.PurposeCode)

	fullURL := fmt.Sprintf("%s?%s", targetURL, query.Encode())

	log.Debugf("请求URL: %s", fullURL)

	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		log.Errorf("创建请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"application/json, text/javascript, */*; q=0.01"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Host":               {"kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/commutation_queryLeftTickets.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"empty"},
		"Sec-Fetch-Mode":     {"cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
	}

	// 必须在设置请求头之后设置cookie，否则会被覆盖
	// 设置关键的_uab_collina cookie（反爬虫机制）
	// 根据HAR分析，这个cookie必须在API请求前动态生成
	SetupUabCollinaCookie(req)

	// 设置其他额外的cookies（模拟浏览器行为）
	SetupAdditionalCookies(req, "出发站", params.FromStation, "到达站", params.ToStation)

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", fullURL)
	PrintRequestCookies(req)
	log.Debug("请求体: [GET请求无请求体]")

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Errorf("请求失败: %v", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取并解析响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		log.Errorf("读取响应失败: %v", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	log.Debugf("响应体: %s", body)

	var ticketResp LeftTicketResponse
	if err := json.Unmarshal(body, &ticketResp); err != nil {
		log.Errorf("解析JSON失败: %v\n原始响应: %s", err, body)
		return nil, fmt.Errorf("解析JSON失败: %v\n原始响应: %s", err, body)
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &ticketResp, nil
}

// ParseTrainList 解析车次信息
func (d *LeftTicketData) ParseTrainList() []string {
	var trains []string
	for _, item := range d.Result {
		if item != "" {
			trains = append(trains, item)
		}
	}
	return trains
}
