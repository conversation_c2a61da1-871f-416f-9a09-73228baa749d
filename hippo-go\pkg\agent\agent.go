package agent

import (
	"fmt"

	"github.com/hypnoswang/hippo-go/pkg/global"
	"github.com/hypnoswang/hippo-go/pkg/log"
)

type Agent struct{}

func NewAgent() *Agent {
	return &Agent{}
}

func (a *Agent) CheckLogin() bool {
	rsp, err := PostConf(ConfUrl)
	if err != nil {
		log.Errorf("PostConf failed: %v", err)
		return false
	} else {
		return rsp.Data.IsLogin == "Y"
	}
}

func (a *Agent) CheckLogin2() bool {
	rsp, err := PostLoginConf(Conf2Url)
	if err != nil {
		log.Errorf("PostLoginConf failed: %v", err)
		return false
	} else {
		return rsp.Data.IsLogin == "Y"
	}
}

func (a *Agent) GetQrCode() (*global.QrCode, error) {
	rsp, err := PostCreateQR64(CreateQrUrl)
	if err != nil {
		log.Errorf("CreateQr failed: %v", err)
		return nil, err
	} else {
		if rsp.ResultCode != "0" {
			log.Errorf("CreateQr failed: code=%s, msg=%s", rsp.ResultCode, rsp.ResultMessage)
			return nil, fmt.Errorf("%s:%s", rsp.ResultCode, rsp.ResultMessage)
		} else {
			rv := &global.QrCode{
				Image: rsp.Image,
				Uuid:  rsp.UUID,
			}

			return rv, nil
		}
	}
}

func (a *Agent) CheckQrCode(uuid string) (bool, error) {
	rsp, err := PostCheckQR(CheckQrUrl, uuid)
	if err != nil {
		log.Errorf("CreateQr failed: %v", err)
		return false, err
	} else {
		if rsp.ResultCode == "2" {
			return true, nil
		} else if rsp.ResultCode == "0" || rsp.ResultCode == "1" {
			return false, nil
		} else {
			log.Errorf("CheckQr failed: code=%s, msg=%s", rsp.ResultCode, rsp.ResultMessage)
			return false, fmt.Errorf("%s:%s", rsp.ResultCode, rsp.ResultMessage)
		}
	}
}

func (a *Agent) InitApi() error {
	rsp, err := PostUAMTK(UamtkUrl)
	if err != nil {
		log.Errorf("UAMTK failed: %v", err)
		return err
	} else if rsp.ResultCode != 0 {
		log.Errorf("UAMTK failed: code=%d, msg=%s", rsp.ResultCode, rsp.ResultMessage)
		return fmt.Errorf("%d:%s", rsp.ResultCode, rsp.ResultMessage)
	}

	appK := rsp.NewAppTK

	err = PostUAMAuthClient(UamauthClientUrl, appK)
	if err != nil {
		log.Errorf("UAMAauthClient failed: %v", err)
		return err
	}

	_ = a.CheckLogin2()

	rsp2, err := PostInitMy12306(InitMy12306ApiUrl)
	if err != nil {
		log.Errorf("InitMy12306 failed: %v", err)
		return err
	} else if rsp2.HttpStatus != 200 {
		log.Errorf("InitMy12306 failed: http status=%s", rsp2.HttpStatus)
		return fmt.Errorf("http status=%d", rsp2.HttpStatus)
	}

	return nil
}

func (a *Agent) QueryJicipiaos() ([]*global.FavorOrderData, error) {
	params := &FavorOrderQueryParams{
		QueryType:  "4",
		StatusFlag: "2",
		PageIndex:  1,
		PageSize:   100,
	}

	rsp, err := PostQueryFavorOrder(QueryFavorOrderMergeInfoNewUrl, params)
	if err != nil {
		return nil, err
	} else {
		if !rsp.Status || rsp.HttpStatus != 200 {
			return nil, fmt.Errorf("status=%t, httpStatus=%d", rsp.Status, rsp.HttpStatus)
		} else {
			favors := rsp.Data
			for _, data := range favors {
				data.Effective = data.OutFlagTmp != "0"
			}

			return favors, nil
		}
	}
}
