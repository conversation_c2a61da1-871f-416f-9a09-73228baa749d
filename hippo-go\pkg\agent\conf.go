package agent

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

// ConfResponse 定义12306配置接口的响应结构
type ConfResponse struct {
	ValidateMessagesShowID string         `json:"validateMessagesShowId"`
	Status                 bool           `json:"status"`
	HTTPStatus             int            `json:"httpstatus"`
	Data                   ConfData       `json:"data"`
	Messages               []string       `json:"messages"`
	ValidateMessages       map[string]any `json:"validateMessages"`
}

// ConfData 定义data字段的详细结构
type ConfData struct {
	IsOpenUpdateHBTime string   `json:"is_open_updateHBTime"`
	IsStudentDate      bool     `json:"isstudentDate"`
	IsMessagePassCode  string   `json:"is_message_passCode"`
	IsPhoneCheck       string   `json:"is_phone_check"`
	StudentDate        []string `json:"studentDate"`
	IsUAMLogin         string   `json:"is_uam_login"`
	IsLoginPassCode    string   `json:"is_login_passCode"`
	IsSweepLogin       string   `json:"is_sweep_login"`
	QueryURL           string   `json:"queryUrl"`
	NowStr             string   `json:"nowStr"`
	PSRQRCodeResult    string   `json:"psr_qr_code_result"`
	Now                int64    `json:"now"`
	YYOpenTime         string   `json:"yy_open_time"`
	LoginURL           string   `json:"login_url"`
	StuControl         int      `json:"stu_control"`
	IsLogin            string   `json:"is_login"`
	IsOlympicLogin     string   `json:"is_olympicLogin"`
	YYTicketQuery      string   `json:"yy_ticket_query"`
	OtherControl       int      `json:"other_control"`
}

// PostConf 发送配置请求并返回解析后的响应结构
func PostConf(targetURL string) (*ConfResponse, error) {
	// 创建请求
	req, err := http.NewRequest("POST", targetURL, bytes.NewBuffer([]byte{}))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             []string{"*/*"},
		"Accept-Encoding":    []string{"gzip, deflate, br, zstd"},
		"Accept-Language":    []string{"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         []string{"keep-alive"},
		"Content-Length":     []string{"0"},
		"Host":               []string{"www.12306.cn"},
		"Origin":             []string{"https://www.12306.cn"},
		"Referer":            []string{"https://www.12306.cn/index/"},
		"Sec-Ch-Ua":          []string{`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   []string{"?0"},
		"Sec-Ch-Ua-Platform": []string{`"Windows"`},
		"Sec-Fetch-Dest":     []string{"empty"},
		"Sec-Fetch-Mode":     []string{"cors"},
		"Sec-Fetch-Site":     []string{"same-origin"},
		"User-Agent":         []string{"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"X-Requested-With":   []string{"XMLHttpRequest"},
	}

	// 打印请求Cookies
	PrintRequestCookies(req)

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 读取响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var confResp ConfResponse
	if err := json.Unmarshal(body, &confResp); err != nil {
		return nil, fmt.Errorf("解析响应JSON失败: %v", err)
	}

	// 打印响应信息
	log.Infof("响应状态: %s", resp.Status)
	log.Debugf("完整响应: %+v", confResp)

	// 打印当前保存的cookies
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return &confResp, nil
}
