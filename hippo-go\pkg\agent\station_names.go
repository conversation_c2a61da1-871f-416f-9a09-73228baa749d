package agent

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"

	"github.com/hypnoswang/hippo-go/pkg/log"
	"github.com/hypnoswang/hippo-go/pkg/utils"
)

func GetStationNameJS(targetURL string) (string, error) {
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header = http.Header{
		"Accept":             {"*/*"},
		"Accept-Encoding":    {"gzip, deflate, br, zstd"},
		"Accept-Language":    {"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6,zh-TW;q=0.5"},
		"Connection":         {"keep-alive"},
		"Host":               {"kyfw.12306.cn"},
		"Referer":            {"https://kyfw.12306.cn/otn/view/commutation_queryLeftTickets.html"},
		"Sec-Ch-Ua":          {`"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"Sec-Ch-Ua-Mobile":   {"?0"},
		"Sec-Ch-Ua-Platform": {`"Windows"`},
		"Sec-Fetch-Dest":     {"script"},
		"Sec-Fetch-Mode":     {"no-cors"},
		"Sec-Fetch-Site":     {"same-origin"},
		"User-Agent":         {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
	}

	// 打印请求信息
	log.Debugf("\n====== 请求 %s ======\n", targetURL)
	PrintRequestCookies(req)
	log.Debug("请求体: [GET请求无请求体]")

	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应头
	log.Debugf("\n====== 响应头 ======")
	for k, v := range resp.Header {
		log.Debugf("%s: %v", k, v)
	}

	// 读取响应体
	body, err := utils.DecompressResponse(resp)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JS响应内容
	content := string(body)
	re := regexp.MustCompile(`var station_names\s*=\s*'(.*)'`)
	matches := re.FindStringSubmatch(content)
	if len(matches) < 2 {
		return "", fmt.Errorf("解析station_names失败，响应格式不符")
	}

	// 打印当前Cookies状态
	u, _ := url.Parse(targetURL)
	PrintCurrentCookies(u)

	return matches[1], nil
}
